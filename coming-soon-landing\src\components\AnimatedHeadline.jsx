import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

const AnimatedHeadline = () => {
  const [displayText, setDisplayText] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isTypingComplete, setIsTypingComplete] = useState(false)
  const [showGlitch, setShowGlitch] = useState(false)

  const fullText = "OPERATION: BLACKOUT"

  useEffect(() => {
    if (currentIndex < fullText.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + fullText[currentIndex])
        setCurrentIndex(prev => prev + 1)
      }, 120) // Typing speed

      return () => clearTimeout(timeout)
    } else {
      setIsTypingComplete(true)
      // Trigger glitch effect randomly
      const glitchInterval = setInterval(() => {
        setShowGlitch(true)
        setTimeout(() => setShowGlitch(false), 200)
      }, 3000)

      return () => clearInterval(glitchInterval)
    }
  }, [currentIndex, fullText])

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 1,
        ease: "easeOut"
      }
    }
  }

  const cursorVariants = {
    blinking: {
      opacity: [0, 0, 1, 1],
      transition: {
        duration: 0.8,
        repeat: Infinity,
        repeatDelay: 0,
        ease: "linear",
        times: [0, 0.5, 0.5, 1]
      }
    }
  }

  return (
    <motion.div
      className="relative text-center"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Glitch Background */}
      <div className="absolute inset-0 opacity-20">
        <h1 className={`text-4xl md:text-6xl lg:text-7xl font-mono font-bold text-red-500 ${showGlitch ? 'glitch' : ''}`} data-text={displayText}>
          {displayText}
        </h1>
      </div>

      {/* Main Hacker Text */}
      <motion.h1
        className={`relative text-4xl md:text-6xl lg:text-7xl font-mono font-bold leading-tight matrix-text ${showGlitch ? 'glitch' : ''}`}
        data-text={displayText}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        {displayText}
        <motion.span
          className="inline-block ml-2 text-green-400 animate-terminal-cursor"
          variants={cursorVariants}
          animate="blinking"
        >
          █
        </motion.span>
      </motion.h1>

      {/* Terminal Subtitle */}
      {isTypingComplete && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.6 }}
          className="mt-8"
        >
          <div className="font-mono text-green-400 space-y-2">
            <p className="text-lg md:text-xl">
              <span className="text-red-500">[CLASSIFIED]</span> Next-Gen Cyber Warfare Platform
            </p>
            <p className="text-sm md:text-base text-yellow-400">
              {'>'} Initializing quantum encryption protocols...
            </p>
            <p className="text-sm md:text-base text-cyan-400">
              {'>'} Neural network interfaces: <span className="text-green-400">ONLINE</span>
            </p>
          </div>
        </motion.div>
      )}

      {/* ASCII Art Border */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.2 }}
        className="mt-8 font-mono text-green-400/50 text-xs"
      >
        <div>╔══════════════════════════════════════╗</div>
        <div>║          SECURITY CLEARANCE          ║</div>
        <div>║             LEVEL: OMEGA             ║</div>
        <div>╚══════════════════════════════════════╝</div>
      </motion.div>
    </motion.div>
  )
}

export default AnimatedHeadline
