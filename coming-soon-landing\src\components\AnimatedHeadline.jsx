import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

const AnimatedHeadline = () => {
  const [displayText, setDisplayText] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isTypingComplete, setIsTypingComplete] = useState(false)
  
  const fullText = "Something Awesome is Coming..."
  
  useEffect(() => {
    if (currentIndex < fullText.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + fullText[currentIndex])
        setCurrentIndex(prev => prev + 1)
      }, 100) // Typing speed
      
      return () => clearTimeout(timeout)
    } else {
      setIsTypingComplete(true)
    }
  }, [currentIndex, fullText])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  const cursorVariants = {
    blinking: {
      opacity: [0, 0, 1, 1],
      transition: {
        duration: 1,
        repeat: Infinity,
        repeatDelay: 0,
        ease: "linear",
        times: [0, 0.5, 0.5, 1]
      }
    }
  }

  const glowVariants = {
    initial: { 
      textShadow: "0 0 10px #00D4FF, 0 0 20px #00D4FF" 
    },
    animate: {
      textShadow: [
        "0 0 10px #00D4FF, 0 0 20px #00D4FF",
        "0 0 20px #00D4FF, 0 0 30px #00D4FF, 0 0 40px #00D4FF",
        "0 0 10px #00D4FF, 0 0 20px #00D4FF"
      ],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  return (
    <motion.div
      className="mb-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.h1 
        className="text-4xl md:text-6xl lg:text-7xl font-orbitron font-bold text-white mb-4 leading-tight"
        variants={isTypingComplete ? glowVariants : {}}
        initial="initial"
        animate={isTypingComplete ? "animate" : "initial"}
      >
        {displayText}
        <motion.span
          className="inline-block ml-1 text-electric-blue"
          variants={cursorVariants}
          animate="blinking"
        >
          |
        </motion.span>
      </motion.h1>
    </motion.div>
  )
}

export default AnimatedHeadline
