import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

const AnimatedHeadline = () => {
  const [displayText, setDisplayText] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isTypingComplete, setIsTypingComplete] = useState(false)

  const fullText = "The Future is Coming"

  useEffect(() => {
    if (currentIndex < fullText.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + fullText[currentIndex])
        setCurrentIndex(prev => prev + 1)
      }, 80) // Typing speed

      return () => clearTimeout(timeout)
    } else {
      setIsTypingComplete(true)
    }
  }, [currentIndex, fullText])

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  }

  const cursorVariants = {
    blinking: {
      opacity: [0, 0, 1, 1],
      transition: {
        duration: 1,
        repeat: Infinity,
        repeatDelay: 0,
        ease: "linear",
        times: [0, 0.5, 0.5, 1]
      }
    }
  }

  return (
    <motion.div
      className="relative"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Background glow effect */}
      <div className="absolute inset-0 blur-3xl opacity-30">
        <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
          {displayText}
        </h1>
      </div>

      {/* Main text */}
      <motion.h1
        className="relative text-5xl md:text-7xl lg:text-8xl font-bold leading-tight"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <span className="bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent">
          {displayText}
        </span>
        <motion.span
          className="inline-block ml-2 text-purple-400"
          variants={cursorVariants}
          animate="blinking"
        >
          |
        </motion.span>
      </motion.h1>

      {/* Subtitle */}
      {isTypingComplete && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="mt-6"
        >
          <p className="text-2xl md:text-3xl font-light text-gradient">
            Something Revolutionary
          </p>
        </motion.div>
      )}
    </motion.div>
  )
}

export default AnimatedHeadline
