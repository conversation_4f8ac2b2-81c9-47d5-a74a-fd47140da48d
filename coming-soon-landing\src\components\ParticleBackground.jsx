import { motion } from 'framer-motion'

const ParticleBackground = () => {
  // Matrix-style digital rain
  const matrixColumns = Array.from({ length: 15 }, (_, i) => ({
    id: i,
    x: (i * 100) / 15,
    characters: Array.from({ length: 20 }, () =>
      Math.random() > 0.5
        ? String.fromCharCode(0x30A0 + Math.random() * 96) // Katakana
        : Math.random().toString(36).charAt(0).toUpperCase()
    ),
    duration: Math.random() * 10 + 15,
    delay: Math.random() * 5,
  }))

  // Floating binary code
  const binaryStrings = Array.from({ length: 8 }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    binary: Array.from({ length: 8 }, () => Math.random() > 0.5 ? '1' : '0').join(''),
    duration: Math.random() * 20 + 25,
    delay: Math.random() * 8,
  }))

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Matrix Digital Rain */}
      {matrixColumns.map((column) => (
        <motion.div
          key={column.id}
          className="absolute top-0 font-mono text-green-400/30 text-sm"
          style={{ left: `${column.x}%` }}
          animate={{
            y: ['-100vh', '100vh'],
          }}
          transition={{
            duration: column.duration,
            repeat: Infinity,
            delay: column.delay,
            ease: "linear",
          }}
        >
          {column.characters.map((char, index) => (
            <motion.div
              key={index}
              className="mb-2"
              animate={{
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 2,
                delay: index * 0.1,
                repeat: Infinity,
              }}
            >
              {char}
            </motion.div>
          ))}
        </motion.div>
      ))}

      {/* Floating Binary Code */}
      {binaryStrings.map((binary) => (
        <motion.div
          key={binary.id}
          className="absolute font-mono text-green-400/20 text-xs"
          style={{
            left: `${binary.x}%`,
            top: `${binary.y}%`,
          }}
          animate={{
            x: [0, Math.random() * 200 - 100, 0],
            y: [0, Math.random() * 200 - 100, 0],
            opacity: [0, 0.5, 0],
            rotate: [0, 360],
          }}
          transition={{
            duration: binary.duration,
            repeat: Infinity,
            delay: binary.delay,
            ease: "easeInOut",
          }}
        >
          {binary.binary}
        </motion.div>
      ))}

      {/* Glowing Circuit Lines */}
      <div className="absolute inset-0">
        {Array.from({ length: 3 }, (_, i) => (
          <motion.div
            key={`circuit-${i}`}
            className="absolute border-green-400/10"
            style={{
              left: `${20 + i * 30}%`,
              top: `${10 + i * 20}%`,
              width: '200px',
              height: '1px',
              borderTop: '1px solid',
            }}
            animate={{
              opacity: [0.1, 0.3, 0.1],
              scaleX: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              delay: i * 2,
            }}
          />
        ))}
      </div>

      {/* Scanning Lines */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-b from-transparent via-green-400/5 to-transparent h-2"
        animate={{
          y: ['-10px', '100vh'],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "linear",
        }}
      />
    </div>
  )
}

export default ParticleBackground
