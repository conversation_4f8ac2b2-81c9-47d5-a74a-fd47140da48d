import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import LoadingScreen from './components/LoadingScreen'
import ParticleBackground from './components/ParticleBackground'
import AnimatedHeadline from './components/AnimatedHeadline'
import CountdownTimer from './components/CountdownTimer'
import NotifyModal from './components/NotifyModal'
import SocialIcons from './components/SocialIcons'

function App() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Set launch date to 30 days from now
  const launchDate = new Date()
  launchDate.setDate(launchDate.getDate() + 30)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  }

  return (
    <>
      <AnimatePresence>
        {isLoading && (
          <LoadingScreen onComplete={() => setIsLoading(false)} />
        )}
      </AnimatePresence>

      {!isLoading && (
        <div className="min-h-screen relative overflow-hidden bg-black">
          {/* Terminal Header */}
          <div className="absolute top-0 left-0 right-0 bg-black border-b-2 border-green-400 p-4 font-mono text-green-400 z-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-red-500">●</span>
                <span className="text-yellow-500">●</span>
                <span className="text-green-500">●</span>
                <span className="ml-4">root@hackersystem:~$</span>
              </div>
              <div className="text-sm">
                {new Date().toLocaleString()} | SECURE CONNECTION ESTABLISHED
              </div>
            </div>
          </div>

          {/* Matrix Rain Background */}
          <div className="absolute inset-0 overflow-hidden">
            {Array.from({ length: 20 }, (_, i) => (
              <div
                key={i}
                className="absolute top-0 text-green-400 font-mono text-sm opacity-20 animate-matrix"
                style={{
                  left: `${i * 5}%`,
                  animationDelay: `${i * 0.5}s`,
                  animationDuration: `${15 + Math.random() * 10}s`
                }}
              >
                {Array.from({ length: 50 }, (_, j) => (
                  <div key={j} className="mb-2">
                    {Math.random().toString(36).substring(2, 8)}
                  </div>
                ))}
              </div>
            ))}
          </div>

          <ParticleBackground />

          {/* Main Terminal Content */}
          <motion.div
            className="relative z-10 flex flex-col items-center justify-center min-h-screen px-6 pt-20"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Terminal Window */}
            <div className="terminal-window max-w-6xl w-full mx-auto p-8">
              {/* Terminal Header */}
              <div className="flex items-center justify-between mb-8 pb-4 border-b border-green-400/30">
                <div className="flex items-center space-x-2 text-green-400 font-mono">
                  <span>[CLASSIFIED]</span>
                  <span className="animate-terminal-cursor">_</span>
                </div>
                <div className="text-green-400 font-mono text-sm">
                  STATUS: INFILTRATION IN PROGRESS...
                </div>
              </div>

              {/* Animated Headline */}
              <motion.div variants={itemVariants} className="mb-12">
                <AnimatedHeadline />
              </motion.div>

              {/* Terminal Output */}
              <motion.div variants={itemVariants} className="mb-12">
                <div className="font-mono text-green-400 space-y-2 text-left max-w-4xl mx-auto">
                  <div className="flex items-center">
                    <span className="text-red-500">root@system:~$</span>
                    <span className="ml-2">./initiate_operation.sh</span>
                  </div>
                  <div className="text-yellow-400">[INFO] Preparing advanced cyber operations...</div>
                  <div className="text-blue-400">[SYSTEM] Quantum encryption protocols loaded</div>
                  <div className="text-green-400">[SUCCESS] Neural network interfaces activated</div>
                  <div className="text-cyan-400">[ALERT] Revolutionary technology deployment imminent</div>
                </div>
              </motion.div>

              {/* Countdown Timer */}
              <motion.div variants={itemVariants} className="mb-12">
                <CountdownTimer targetDate={launchDate} />
              </motion.div>

              {/* Hacker Buttons */}
              <motion.div variants={itemVariants} className="mb-16">
                <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                  <button
                    onClick={() => setIsModalOpen(true)}
                    className="btn-hack text-lg px-8 py-4 relative group"
                  >
                    <span className="relative z-10">GAIN ACCESS</span>
                    <div className="absolute inset-0 bg-green-400/10 transform skew-x-12 group-hover:skew-x-0 transition-transform duration-300"></div>
                  </button>
                  <button className="border-2 border-red-500 text-red-500 px-8 py-4 font-mono font-bold uppercase tracking-wider hover:bg-red-500 hover:text-black transition-all duration-300 relative overflow-hidden group">
                    <span className="relative z-10">ABORT MISSION</span>
                    <div className="absolute inset-0 bg-red-500 transform translate-x-full group-hover:translate-x-0 transition-transform duration-300"></div>
                  </button>
                </div>
              </motion.div>

              {/* System Status */}
              <motion.div variants={itemVariants} className="text-center">
                <div className="inline-block border border-green-400/50 bg-black/50 px-6 py-3 font-mono text-green-400">
                  <div className="flex items-center space-x-4">
                    <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                    <span>SECURE CONNECTION ACTIVE</span>
                    <span className="text-yellow-400">|</span>
                    <span>ENCRYPTION: AES-256</span>
                    <span className="text-yellow-400">|</span>
                    <span>AGENTS ONLINE: 1,337</span>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>

        {/* Modal */}
        <NotifyModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
        </div>
      )}
    </>
  )
}

export default App
