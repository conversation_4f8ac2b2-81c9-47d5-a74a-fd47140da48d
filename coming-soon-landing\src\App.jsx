import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import LoadingScreen from './components/LoadingScreen'
import ParticleBackground from './components/ParticleBackground'
import AnimatedHeadline from './components/AnimatedHeadline'
import CountdownTimer from './components/CountdownTimer'
import NotifyModal from './components/NotifyModal'
import SocialIcons from './components/SocialIcons'

function App() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Set launch date to 30 days from now
  const launchDate = new Date()
  launchDate.setDate(launchDate.getDate() + 30)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  }

  return (
    <>
      <AnimatePresence>
        {isLoading && (
          <LoadingScreen onComplete={() => setIsLoading(false)} />
        )}
      </AnimatePresence>

      {!isLoading && (
        <div className="min-h-screen relative overflow-hidden">
          {/* Animated Background */}
          <div className="absolute inset-0">
            <div className="absolute top-20 left-20 w-72 h-72 bg-purple-500/20 rounded-full blur-3xl animate-float"></div>
            <div className="absolute top-40 right-20 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
            <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-violet-500/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '4s' }}></div>

            {/* Grid Pattern */}
            <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"></div>

            {/* Spotlight Effect */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[800px] h-[800px] bg-gradient-radial from-purple-500/10 via-transparent to-transparent rounded-full"></div>
          </div>

          <ParticleBackground />

          {/* Main Content */}
          <motion.div
            className="relative z-10 flex flex-col items-center justify-center min-h-screen px-6 text-center"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Hero Section */}
            <div className="max-w-6xl mx-auto">
              {/* Animated Headline */}
              <motion.div variants={itemVariants} className="mb-8">
                <AnimatedHeadline />
              </motion.div>

              {/* Subheading */}
              <motion.div variants={itemVariants} className="mb-16">
                <p className="text-xl md:text-2xl text-gray-300 mb-4 max-w-3xl mx-auto leading-relaxed font-light">
                  We're crafting something extraordinary that will revolutionize your experience.
                </p>
                <p className="text-lg text-gray-400 max-w-2xl mx-auto">
                  Join thousands of early adopters waiting for the launch.
                </p>
              </motion.div>

              {/* Countdown Timer */}
              <motion.div variants={itemVariants} className="mb-16">
                <CountdownTimer targetDate={launchDate} />
              </motion.div>

              {/* Call to Action Section */}
              <motion.div variants={itemVariants} className="mb-20">
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <button
                    onClick={() => setIsModalOpen(true)}
                    className="btn-primary text-lg px-12 py-4 font-semibold tracking-wide"
                  >
                    Get Early Access
                  </button>
                  <button className="px-8 py-4 border border-white/20 rounded-full text-white hover:bg-white/5 transition-all duration-300 backdrop-blur-sm">
                    Learn More
                  </button>
                </div>
              </motion.div>

              {/* Social Icons */}
              <motion.div variants={itemVariants}>
                <SocialIcons />
              </motion.div>
            </div>
          </motion.div>

        {/* Modal */}
        <NotifyModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
        </div>
      )}
    </>
  )
}

export default App
