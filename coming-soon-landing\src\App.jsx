import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import LoadingScreen from './components/LoadingScreen'
import ParticleBackground from './components/ParticleBackground'
import AnimatedHeadline from './components/AnimatedHeadline'
import CountdownTimer from './components/CountdownTimer'
import NotifyModal from './components/NotifyModal'
import SocialIcons from './components/SocialIcons'

function App() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Set launch date to 30 days from now
  const launchDate = new Date()
  launchDate.setDate(launchDate.getDate() + 30)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  }

  return (
    <>
      <AnimatePresence>
        {isLoading && (
          <LoadingScreen onComplete={() => setIsLoading(false)} />
        )}
      </AnimatePresence>

      {!isLoading && (
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-black relative overflow-hidden">
      {/* Background Effects */}
      <ParticleBackground />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-electric-blue/10 via-transparent to-transparent"></div>
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-cyber-purple/20 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-electric-blue/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '1s' }}></div>

      {/* Main Content */}
      <motion.div
        className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 text-center"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Animated Headline */}
        <motion.div variants={itemVariants}>
          <AnimatedHeadline />
        </motion.div>

        {/* Subheading */}
        <motion.p
          className="text-lg md:text-xl text-gray-300 mb-12 max-w-2xl leading-relaxed"
          variants={itemVariants}
        >
          We're working hard to bring you something amazing.
        </motion.p>

        {/* Countdown Timer */}
        <motion.div variants={itemVariants} className="mb-12">
          <CountdownTimer targetDate={launchDate} />
        </motion.div>

        {/* Call to Action Button */}
        <motion.div variants={itemVariants} className="mb-16">
          <button
            onClick={() => setIsModalOpen(true)}
            className="btn-primary btn-glow text-lg px-10 py-4 font-orbitron font-semibold tracking-wide uppercase"
          >
            Notify Me
          </button>
        </motion.div>

        {/* Social Icons */}
        <motion.div variants={itemVariants}>
          <SocialIcons />
        </motion.div>
      </motion.div>

        {/* Modal */}
        <NotifyModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
        </div>
      )}
    </>
  )
}

export default App
