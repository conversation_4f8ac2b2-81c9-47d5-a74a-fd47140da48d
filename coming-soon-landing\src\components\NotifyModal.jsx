import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

const NotifyModal = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isValidEmail, setIsValidEmail] = useState(true)
  const [isLoading, setIsLoading] = useState(false)

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateEmail(email)) {
      setIsValidEmail(false)
      return
    }

    setIsLoading(true)
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      setIsSubmitted(true)
      
      // Reset form after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false)
        setEmail('')
        onClose()
      }, 3000)
    }, 1500)
  }

  const handleEmailChange = (e) => {
    setEmail(e.target.value)
    if (!isValidEmail) {
      setIsValidEmail(true)
    }
  }

  const handleClose = () => {
    setEmail('')
    setIsSubmitted(false)
    setIsValidEmail(true)
    setIsLoading(false)
    onClose()
  }

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  }

  const modalVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.8,
      y: 50
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 300
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      y: 50,
      transition: {
        duration: 0.2
      }
    }
  }

  const successVariants = {
    hidden: { opacity: 0, scale: 0.5 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        type: "spring",
        damping: 20,
        stiffness: 300
      }
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          {/* Backdrop */}
          <motion.div
            className="absolute inset-0 bg-black/70 backdrop-blur-sm"
            onClick={handleClose}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          />

          {/* Hacker Modal */}
          <motion.div
            className="relative max-w-2xl w-full mx-4"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {/* Terminal Window */}
            <div className="terminal-window p-0 overflow-hidden">
              {/* Terminal Header */}
              <div className="bg-black border-b-2 border-green-400 p-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                  <span className="font-mono text-green-400 text-sm">root@secure-access:/home/<USER>/span>
                </div>
                <div className="font-mono text-green-400 text-xs">
                  SECURE_TERMINAL_v2.1.337
                </div>
              </div>

              {/* Terminal Content */}
              <div className="bg-black p-8 relative">
                {/* Matrix background effect */}
                <div className="absolute inset-0 opacity-5">
                  {Array.from({ length: 10 }, (_, i) => (
                    <div key={i} className="absolute text-green-400 font-mono text-xs animate-matrix"
                         style={{ left: `${i * 10}%`, animationDelay: `${i * 0.2}s` }}>
                      {Array.from({ length: 20 }, (_, j) => (
                        <div key={j}>{Math.random().toString(36).substring(2, 4)}</div>
                      ))}
                    </div>
                  ))}
                </div>

                {/* Close Button */}
                <button
                  onClick={handleClose}
                  className="absolute top-4 right-4 text-red-500 hover:text-red-400 transition-colors duration-200 font-mono text-lg z-20"
                  aria-label="Close terminal"
                >
                  [X]
                </button>

                <div className="relative z-10">
                  {!isSubmitted ? (
                    <>
                      <motion.div
                        className="mb-8"
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                      >
                        <div className="font-mono text-green-400 space-y-2">
                          <div className="text-lg">
                            <span className="text-red-500">[SYSTEM]</span> ACCESS REQUEST INITIATED
                          </div>
                          <div className="text-sm text-yellow-400">
                            {'>'} Requesting security clearance...
                          </div>
                          <div className="text-sm text-cyan-400">
                            {'>'} Neural interface calibration required
                          </div>
                          <div className="text-sm text-green-400">
                            {'>'} Enter credentials to proceed:
                          </div>
                        </div>

                        <div className="mt-6 border border-green-400/30 bg-black/50 p-4">
                          <div className="font-mono text-green-400 text-center">
                            <div className="text-xl font-bold mb-2">CLASSIFIED ACCESS</div>
                            <div className="text-sm">SECURITY LEVEL: OMEGA</div>
                          </div>
                        </div>
                      </motion.div>

                      <motion.form
                        onSubmit={handleSubmit}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        className="space-y-6"
                      >
                        <div className="relative">
                          <div className="font-mono text-green-400 text-sm mb-2">
                            {'>'} EMAIL_ADDRESS:
                          </div>
                          <input
                            type="email"
                            value={email}
                            onChange={handleEmailChange}
                            placeholder="<EMAIL>"
                            className={`w-full px-4 py-3 bg-black border-2 font-mono text-green-400 placeholder-green-400/50 focus:outline-none transition-all duration-300 ${
                              isValidEmail
                                ? 'border-green-400 focus:border-green-300 focus:shadow-lg focus:shadow-green-400/20'
                                : 'border-red-500 focus:border-red-400'
                            }`}
                            required
                            disabled={isLoading}
                          />
                          {!isValidEmail && (
                            <motion.p
                              className="text-red-400 text-sm mt-2 font-mono flex items-center"
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                            >
                              <span className="text-red-500">[ERROR]</span> Invalid email format detected
                            </motion.p>
                          )}
                        </div>

                        <button
                          type="submit"
                          disabled={isLoading}
                          className="w-full btn-hack font-mono py-4 text-lg disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden"
                        >
                          {isLoading ? (
                            <div className="flex items-center justify-center font-mono">
                              <div className="mr-3 text-green-400">
                                {'>'} PROCESSING...
                              </div>
                              <div className="w-2 h-2 bg-green-400 animate-pulse"></div>
                            </div>
                          ) : (
                            'INITIATE ACCESS PROTOCOL'
                          )}
                        </button>

                        <div className="text-center font-mono text-green-400/70 text-sm space-y-1">
                          <div>AGENTS_REGISTERED: <span className="text-green-400">1,337</span></div>
                          <div>SECURITY_STATUS: <span className="text-yellow-400">ENCRYPTED</span></div>
                          <div>CLEARANCE_LEVEL: <span className="text-red-500">CLASSIFIED</span></div>
                        </div>
                      </motion.form>
                </>
                  ) : (
                    <motion.div
                      className="text-center py-12"
                      variants={successVariants}
                      initial="hidden"
                      animate="visible"
                    >
                      <motion.div
                        className="font-mono text-green-400 space-y-3 mb-8"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.2 }}
                      >
                        <div className="text-lg">
                          <span className="text-green-400">[SUCCESS]</span> ACCESS GRANTED
                        </div>
                        <div className="text-sm text-yellow-400">
                          {'>'} Neural link established...
                        </div>
                        <div className="text-sm text-cyan-400">
                          {'>'} Quantum encryption activated...
                        </div>
                        <div className="text-sm text-green-400">
                          {'>'} Agent credentials verified ✓
                        </div>
                      </motion.div>

                      <motion.div
                        className="border-2 border-green-400 bg-black/80 p-6 mb-6"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", delay: 0.4 }}
                      >
                        <div className="font-mono text-green-400 text-center">
                          <div className="text-2xl font-bold mb-2">AGENT REGISTERED</div>
                          <div className="text-sm">CLEARANCE: OMEGA</div>
                          <div className="text-sm">STATUS: ACTIVE</div>
                        </div>
                      </motion.div>

                      <motion.div
                        className="font-mono text-green-400/70 text-sm space-y-1"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.8 }}
                      >
                        <div>NOTIFICATION_PROTOCOL: <span className="text-green-400">ENABLED</span></div>
                        <div>MISSION_STATUS: <span className="text-yellow-400">STANDBY</span></div>
                        <div>NEXT_CONTACT: <span className="text-cyan-400">CLASSIFIED</span></div>
                      </motion.div>
                    </motion.div>
                  )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default NotifyModal
