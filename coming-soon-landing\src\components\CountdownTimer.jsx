import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

const CountdownTimer = ({ targetDate }) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  useEffect(() => {
    const calculateTimeLeft = () => {
      const difference = +new Date(targetDate) - +new Date()
      
      if (difference > 0) {
        return {
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
          minutes: Math.floor((difference / 1000 / 60) % 60),
          seconds: Math.floor((difference / 1000) % 60)
        }
      }
      
      return { days: 0, hours: 0, minutes: 0, seconds: 0 }
    }

    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)

    // Initial calculation
    setTimeLeft(calculateTimeLeft())

    return () => clearInterval(timer)
  }, [targetDate])

  const TimeUnit = ({ value, label, delay = 0 }) => {
    const [prevValue, setPrevValue] = useState(value)
    
    useEffect(() => {
      if (value !== prevValue) {
        setPrevValue(value)
      }
    }, [value, prevValue])

    const cardVariants = {
      hidden: { 
        opacity: 0, 
        scale: 0.8,
        rotateX: -90
      },
      visible: { 
        opacity: 1, 
        scale: 1,
        rotateX: 0,
        transition: {
          duration: 0.6,
          delay: delay,
          ease: "easeOut"
        }
      }
    }

    const numberVariants = {
      initial: { y: 0 },
      animate: { 
        y: value !== prevValue ? [-20, 0] : 0,
        transition: {
          duration: 0.3,
          ease: "easeOut"
        }
      }
    }

    return (
      <motion.div
        className="flex flex-col items-center"
        variants={cardVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="card-glass p-4 md:p-6 rounded-xl shadow-2xl min-w-[80px] md:min-w-[100px] relative overflow-hidden">
          {/* Background glow effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-electric-blue/20 to-cyber-purple/20 rounded-xl"></div>
          
          <div className="relative z-10">
            <AnimatePresence mode="wait">
              <motion.div
                key={value}
                className="text-3xl md:text-4xl lg:text-5xl font-orbitron font-bold text-white text-center"
                variants={numberVariants}
                initial="initial"
                animate="animate"
              >
                {String(value).padStart(2, '0')}
              </motion.div>
            </AnimatePresence>
          </div>
          
          {/* Shine effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform translate-x-[-100%] animate-pulse"></div>
        </div>
        
        <motion.p 
          className="text-sm md:text-base text-gray-300 mt-2 font-inter uppercase tracking-wider"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: delay + 0.3 }}
        >
          {label}
        </motion.p>
      </motion.div>
    )
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  return (
    <motion.div
      className="flex flex-col items-center"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.h2 
        className="text-xl md:text-2xl font-inter font-semibold text-gray-200 mb-8 text-center"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        Launch Countdown
      </motion.h2>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
        <TimeUnit value={timeLeft.days} label="Days" delay={0} />
        <TimeUnit value={timeLeft.hours} label="Hours" delay={0.1} />
        <TimeUnit value={timeLeft.minutes} label="Minutes" delay={0.2} />
        <TimeUnit value={timeLeft.seconds} label="Seconds" delay={0.3} />
      </div>
    </motion.div>
  )
}

export default CountdownTimer
