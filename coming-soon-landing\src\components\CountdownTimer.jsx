import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

const CountdownTimer = ({ targetDate }) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  useEffect(() => {
    const calculateTimeLeft = () => {
      const difference = +new Date(targetDate) - +new Date()
      
      if (difference > 0) {
        return {
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
          minutes: Math.floor((difference / 1000 / 60) % 60),
          seconds: Math.floor((difference / 1000) % 60)
        }
      }
      
      return { days: 0, hours: 0, minutes: 0, seconds: 0 }
    }

    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)

    // Initial calculation
    setTimeLeft(calculateTimeLeft())

    return () => clearInterval(timer)
  }, [targetDate])

  const TimeUnit = ({ value, label, delay = 0 }) => {
    const [prevValue, setPrevValue] = useState(value)

    useEffect(() => {
      if (value !== prevValue) {
        setPrevValue(value)
      }
    }, [value, prevValue])

    const cardVariants = {
      hidden: {
        opacity: 0,
        scale: 0.8,
        y: 50
      },
      visible: {
        opacity: 1,
        scale: 1,
        y: 0,
        transition: {
          duration: 0.8,
          delay: delay,
          ease: "easeOut"
        }
      }
    }

    const numberVariants = {
      initial: { y: 0 },
      animate: {
        y: value !== prevValue ? [-30, 0] : 0,
        transition: {
          duration: 0.5,
          ease: "easeOut"
        }
      }
    }

    return (
      <motion.div
        className="flex flex-col items-center group"
        variants={cardVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="relative">
          {/* Outer glow ring */}
          <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000"></div>

          {/* Main card */}
          <div className="relative card-glass p-6 md:p-8 rounded-2xl min-w-[100px] md:min-w-[120px] lg:min-w-[140px] hover:scale-105 transition-transform duration-300">
            {/* Background pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-2xl"></div>

            {/* Number display */}
            <div className="relative z-10">
              <AnimatePresence mode="wait">
                <motion.div
                  key={value}
                  className="text-4xl md:text-5xl lg:text-6xl font-bold text-white text-center font-mono"
                  variants={numberVariants}
                  initial="initial"
                  animate="animate"
                >
                  {String(value).padStart(2, '0')}
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Animated border */}
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500 via-blue-500 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500"
                 style={{
                   background: 'linear-gradient(45deg, transparent, rgba(139, 92, 246, 0.1), transparent)',
                   animation: 'rotate 3s linear infinite'
                 }}>
            </div>
          </div>
        </div>

        <motion.p
          className="text-sm md:text-base text-gray-400 mt-4 font-medium uppercase tracking-[0.2em] group-hover:text-gray-300 transition-colors duration-300"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: delay + 0.4 }}
        >
          {label}
        </motion.p>
      </motion.div>
    )
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  return (
    <motion.div
      className="flex flex-col items-center max-w-4xl mx-auto"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div
        className="text-center mb-12"
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <h2 className="text-2xl md:text-3xl font-bold text-white mb-2">
          Launch Countdown
        </h2>
        <p className="text-gray-400 text-lg">
          Every second brings us closer to something amazing
        </p>
      </motion.div>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 lg:gap-12 w-full">
        <TimeUnit value={timeLeft.days} label="Days" delay={0} />
        <TimeUnit value={timeLeft.hours} label="Hours" delay={0.1} />
        <TimeUnit value={timeLeft.minutes} label="Minutes" delay={0.2} />
        <TimeUnit value={timeLeft.seconds} label="Seconds" delay={0.3} />
      </div>
    </motion.div>
  )
}

export default CountdownTimer
