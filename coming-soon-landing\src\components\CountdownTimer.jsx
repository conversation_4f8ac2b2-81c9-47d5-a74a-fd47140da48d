import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

const CountdownTimer = ({ targetDate }) => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  useEffect(() => {
    const calculateTimeLeft = () => {
      const difference = +new Date(targetDate) - +new Date()
      
      if (difference > 0) {
        return {
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
          minutes: Math.floor((difference / 1000 / 60) % 60),
          seconds: Math.floor((difference / 1000) % 60)
        }
      }
      
      return { days: 0, hours: 0, minutes: 0, seconds: 0 }
    }

    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)

    // Initial calculation
    setTimeLeft(calculateTimeLeft())

    return () => clearInterval(timer)
  }, [targetDate])

  const TimeUnit = ({ value, label, delay = 0 }) => {
    const [prevValue, setPrevValue] = useState(value)

    useEffect(() => {
      if (value !== prevValue) {
        setPrevValue(value)
      }
    }, [value, prevValue])

    const cardVariants = {
      hidden: {
        opacity: 0,
        scale: 0.8,
        rotateX: -90
      },
      visible: {
        opacity: 1,
        scale: 1,
        rotateX: 0,
        transition: {
          duration: 0.8,
          delay: delay,
          ease: "easeOut"
        }
      }
    }

    const numberVariants = {
      initial: { y: 0 },
      animate: {
        y: value !== prevValue ? [-20, 0] : 0,
        transition: {
          duration: 0.3,
          ease: "easeOut"
        }
      }
    }

    return (
      <motion.div
        className="flex flex-col items-center group"
        variants={cardVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="relative hack-border">
          {/* Terminal-style display */}
          <div className="terminal-window p-6 md:p-8 min-w-[120px] md:min-w-[140px] lg:min-w-[160px] group-hover:shadow-lg group-hover:shadow-green-400/20 transition-all duration-300">
            {/* Terminal header */}
            <div className="flex justify-between items-center mb-2 text-xs text-green-400/70">
              <span>SYS</span>
              <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
            </div>

            {/* Number display */}
            <div className="relative">
              <AnimatePresence mode="wait">
                <motion.div
                  key={value}
                  className="text-4xl md:text-5xl lg:text-6xl font-mono font-bold text-green-400 text-center matrix-text"
                  variants={numberVariants}
                  initial="initial"
                  animate="animate"
                >
                  {String(value).padStart(2, '0')}
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Binary decoration */}
            <div className="text-xs text-green-400/30 font-mono mt-2 text-center">
              {value.toString(2).padStart(8, '0')}
            </div>
          </div>
        </div>

        <motion.div
          className="mt-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: delay + 0.4 }}
        >
          <p className="text-sm md:text-base text-green-400 font-mono uppercase tracking-wider mb-1">
            {label}
          </p>
          <div className="text-xs text-green-400/50 font-mono">
            [{label.toUpperCase()}_MODULE]
          </div>
        </motion.div>
      </motion.div>
    )
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  return (
    <motion.div
      className="flex flex-col items-center max-w-6xl mx-auto"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div
        className="text-center mb-12"
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        <div className="font-mono text-green-400 mb-4">
          <div className="text-lg md:text-xl">
            <span className="text-red-500">[ALERT]</span> OPERATION COUNTDOWN INITIATED
          </div>
          <div className="text-sm text-yellow-400 mt-2">
            {'>'} Quantum breach sequence: <span className="text-green-400">ACTIVE</span>
          </div>
        </div>
        <div className="border border-green-400/30 bg-black/50 px-4 py-2 inline-block font-mono text-green-400 text-sm">
          TIME TO INFILTRATION COMPLETE
        </div>
      </motion.div>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 lg:gap-12 w-full">
        <TimeUnit value={timeLeft.days} label="Days" delay={0} />
        <TimeUnit value={timeLeft.hours} label="Hours" delay={0.1} />
        <TimeUnit value={timeLeft.minutes} label="Minutes" delay={0.2} />
        <TimeUnit value={timeLeft.seconds} label="Seconds" delay={0.3} />
      </div>

      {/* System Status */}
      <motion.div
        className="mt-12 font-mono text-green-400/70 text-sm text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
      >
        <div className="space-y-1">
          <div>ENCRYPTION_LEVEL: QUANTUM_256</div>
          <div>FIREWALL_STATUS: <span className="text-red-500">BYPASSED</span></div>
          <div>NEURAL_LINK: <span className="text-green-400">ESTABLISHED</span></div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default CountdownTimer
