@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Orbitron:wght@400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply box-border;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply font-inter bg-gradient-to-br from-slate-900 to-black text-white antialiased;
    margin: 0;
    min-height: 100vh;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-electric-blue to-cyber-purple text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg;
  }

  .btn-glow {
    @apply animate-pulse-glow;
  }

  .text-glow {
    @apply animate-glow;
  }

  .card-glass {
    @apply bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .text-shadow-glow {
    text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
  }
}