@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600;700;800&family=Orbitron:wght@400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply box-border;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply font-mono bg-black text-green-400 antialiased overflow-x-hidden;
    margin: 0;
    min-height: 100vh;
    background:
      linear-gradient(0deg, transparent 24%, rgba(0, 255, 0, 0.03) 25%, rgba(0, 255, 0, 0.03) 26%, transparent 27%, transparent 74%, rgba(0, 255, 0, 0.03) 75%, rgba(0, 255, 0, 0.03) 76%, transparent 77%, transparent),
      linear-gradient(90deg, transparent 24%, rgba(0, 255, 0, 0.03) 25%, rgba(0, 255, 0, 0.03) 26%, transparent 27%, transparent 74%, rgba(0, 255, 0, 0.03) 75%, rgba(0, 255, 0, 0.03) 76%, transparent 77%, transparent),
      #000;
    background-size: 50px 50px;
    cursor: crosshair;
  }

  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(0deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 0, 0.03) 2px,
        rgba(0, 255, 0, 0.03) 4px);
    pointer-events: none;
    z-index: 1000;
  }
}

@layer components {
  .btn-hack {
    @apply relative bg-black border-2 border-green-400 text-green-400 font-mono font-bold py-3 px-8 transition-all duration-300 uppercase tracking-wider;
    box-shadow:
      0 0 10px rgba(0, 255, 0, 0.3),
      inset 0 0 10px rgba(0, 255, 0, 0.1);
    background: linear-gradient(45deg, transparent 30%, rgba(0, 255, 0, 0.1) 50%, transparent 70%);
    background-size: 200% 200%;
    animation: hackGlow 2s ease-in-out infinite alternate;
  }

  .btn-hack:hover {
    @apply bg-green-400 text-black;
    box-shadow:
      0 0 20px rgba(0, 255, 0, 0.8),
      0 0 40px rgba(0, 255, 0, 0.4),
      inset 0 0 20px rgba(0, 255, 0, 0.2);
    animation: hackPulse 0.1s ease-in-out infinite;
  }

  .terminal-window {
    @apply bg-black border-2 border-green-400 rounded-none font-mono;
    box-shadow:
      0 0 20px rgba(0, 255, 0, 0.3),
      inset 0 0 20px rgba(0, 255, 0, 0.05);
    background:
      linear-gradient(90deg, transparent 79px, rgba(0, 255, 0, 0.03) 81px),
      linear-gradient(rgba(0, 255, 0, 0.03) 50%, transparent 50%);
    background-size: 81px 2px;
  }

  .matrix-text {
    @apply text-green-400 font-mono;
    text-shadow: 0 0 10px rgba(0, 255, 0, 0.8);
    animation: matrixGlow 1.5s ease-in-out infinite alternate;
  }

  .hack-border {
    position: relative;
  }

  .hack-border::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ff00, transparent, #00ff00, transparent, #00ff00);
    background-size: 400% 400%;
    animation: hackBorder 3s ease-in-out infinite;
    z-index: -1;
  }

  .glitch {
    position: relative;
    animation: glitch 2s infinite;
  }

  .glitch::before,
  .glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .glitch::before {
    animation: glitch-1 0.5s infinite;
    color: #ff0000;
    z-index: -1;
  }

  .glitch::after {
    animation: glitch-2 0.5s infinite;
    color: #00ffff;
    z-index: -2;
  }
}

@layer utilities {
  .animate-matrix {
    animation: matrixRain 20s linear infinite;
  }

  .animate-hack-pulse {
    animation: hackPulse 0.1s ease-in-out infinite;
  }

  .animate-terminal-cursor {
    animation: terminalCursor 1s ease-in-out infinite;
  }
}

@keyframes hackGlow {
  0% {
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
    background-position: 0% 50%;
  }

  100% {
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.6);
    background-position: 100% 50%;
  }
}

@keyframes hackPulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.02);
  }
}

@keyframes hackBorder {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes matrixGlow {
  0% {
    text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
    opacity: 0.8;
  }

  100% {
    text-shadow: 0 0 20px rgba(0, 255, 0, 1), 0 0 30px rgba(0, 255, 0, 0.8);
    opacity: 1;
  }
}

@keyframes matrixRain {
  0% {
    transform: translateY(-100vh);
    opacity: 0;
  }

  10% {
    opacity: 1;
  }

  90% {
    opacity: 1;
  }

  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

@keyframes terminalCursor {

  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0;
  }
}

@keyframes glitch {

  0%,
  100% {
    transform: translate(0);
  }

  20% {
    transform: translate(-2px, 2px);
  }

  40% {
    transform: translate(-2px, -2px);
  }

  60% {
    transform: translate(2px, 2px);
  }

  80% {
    transform: translate(2px, -2px);
  }
}

@keyframes glitch-1 {

  0%,
  100% {
    transform: translate(0);
  }

  20% {
    transform: translate(-1px, 1px);
  }

  40% {
    transform: translate(-1px, -1px);
  }

  60% {
    transform: translate(1px, 1px);
  }

  80% {
    transform: translate(1px, -1px);
  }
}

@keyframes glitch-2 {

  0%,
  100% {
    transform: translate(0);
  }

  20% {
    transform: translate(1px, -1px);
  }

  40% {
    transform: translate(1px, 1px);
  }

  60% {
    transform: translate(-1px, -1px);
  }

  80% {
    transform: translate(-1px, 1px);
  }
}